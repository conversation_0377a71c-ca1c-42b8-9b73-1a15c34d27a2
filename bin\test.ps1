#Requires -Version 5.1
#Requires -<PERSON><PERSON><PERSON> @{ ModuleName = 'BuildHelpers'; ModuleVersion = '2.0.1' }
#Requires -<PERSON><PERSON><PERSON> @{ ModuleName = 'Pester'; ModuleVersion = '5.2.0' }

$pesterConfig = New-PesterConfiguration -Hashtable @{
    Run    = @{
        Path     = "$PSScriptRoot/.."
        PassThru = $true
    }
    Output = @{
        Verbosity = 'Detailed'
    }
}
$result = Invoke-Pester -Configuration $pesterConfig
exit $result.FailedCount
