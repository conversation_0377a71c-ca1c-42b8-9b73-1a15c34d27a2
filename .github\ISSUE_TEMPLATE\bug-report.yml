name: 🐛 Bug Report
description: Open an issue about a bug that needs fixing.
title: "[Bug]: "
labels: ["bug"]
body:
- type: checkboxes
  attributes:
    label: Prerequisites
    options:
    - label: I have written a descriptive issue title.
      required: true
    - label: I have searched all issues/PRs to ensure it has not already been reported or fixed.
      required: true
    - label: I have verified that I am using the latest version of Scoop and corresponding bucket.
      required: true
- type: input
  attributes:
    label: Package Name
    description: Name of package (install name) which has bug(s)
    placeholder: e.g. 7zip (not '7-Zip')
  validations:
    required: true
- type: textarea
  attributes:
    label: Expected/Current Behaviour
    description: A clear and concise description of what you expected to happen and what actually happen.
    placeholder: I am experiencing a problem with X. I think Y should be happening but Z is actually happening.
  validations:
    required: true
- type: textarea
  attributes:
    label: Steps to Reproduce
    description: List of steps, sample code or failing test that reproduces the behavior.
    render: console
    placeholder: |
      PS> scoop install tests/meson
      Installing 'meson' (0.61.1) [64bit]
      Loading meson-0.61.1-64.msi from cache.
      Checking hash of meson-0.61.1-64.msi ... ok.
      Extracting meson-0.61.1-64.msi ... done.
      Running installer script...
      Linking D:\Scoop\apps\meson\current => D:\Scoop\apps\meson\0.61.1
      Creating shim for 'meson'.
      Can't shim 'meson.exe': File doesn't exist.
  validations:
    required: true
- type: textarea
  attributes:
    label: Possible Solution
    description: Do you have some suggestions on a fix for the bug?
    placeholder: I am experiencing a problem with X. I think Y should be happening but Z is actually happening.
  validations:
    required: true
- type: textarea
  attributes:
    label: Scoop and Buckets Version
    description: Paste verbatim output from `scoop --version` below.
    render: console
    placeholder: |
      PS> scoop --version
      Current Scoop version:
      c60df9cd (HEAD -> develop, origin/develop) docs(changelog): Prepare for version 0.3.1 (#5248)

      'extras' bucket:
      ea314b213 (HEAD -> master, origin/master, origin/HEAD) lazygit: Update to version 0.36.0

      'main' bucket:
      c6e688d4d (HEAD -> master, origin/master, origin/HEAD) x265: Update to version 3.5+68-40e37bc
  validations:
    required: true
- type: textarea
  attributes:
    label: Scoop Config
    description: Paste verbatim output from `scoop config` below.
    render: console
    placeholder: |
      PS> scoop config

      last_update            : 2022/11/14 22:05:50
      scoop_repo             : https://github.com/ScoopInstaller/Scoop
      scoop_branch           : develop
      use_lessmsi            : True
      aria2-enabled          : True
      aria2-warning-enabled  : False
  validations:
    required: true
- type: textarea
  attributes:
    label: PowerShell Version
    description: Paste verbatim output from `$PSVersionTable` below.
    render: console
    placeholder: |
      PS> $PSVersionTable

      Name                           Value
      ----                           -----
      PSVersion                      7.3.0
      PSEdition                      Core
      GitCommitId                    7.3.0
      OS                             Microsoft Windows 10.0.25236
      Platform                       Win32NT
      PSCompatibleVersions           {1.0, 2.0, 3.0, 4.0…}
      PSRemotingProtocolVersion      2.3
      SerializationVersion           *******
      WSManStackVersion              3.0
  validations:
    required: true
- type: textarea
  attributes:
    label: Additional Softwares
    description: List any additional software that you are using and may be related to this bug.
  validations:
    required: false
