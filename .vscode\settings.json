// Configure PSScriptAnalyzer settings
{
    "[powershell]": {
        "editor.formatOnSave": true
    },
    "powershell.scriptAnalysis.settingsPath": "PSScriptAnalyzerSettings.psd1",
    "powershell.codeFormatting.preset": "OTBS",
    "powershell.codeFormatting.alignPropertyValuePairs": true,
    "powershell.codeFormatting.ignoreOneLineBlock": true,
    "json.schemas": [
        {
            "url": "https://raw.githubusercontent.com/ScoopInstaller/Scoop/master/schema.json",
            "fileMatch": [
                "bucket/**/*.json"
            ]
        }
    ],
    "files.exclude": {
        "**/.git": true,
        "**/.svn": true,
        "**/.hg": true,
        "**/CVS": true,
        "**/.DS_Store": true,
        "**/tmp": true
    }
}
