name: Tests

on:
  push:
    branches:
      - 'main'
      - 'master'
  pull_request:
  workflow_dispatch:

jobs:
  test_powershell:
    name: WindowsPowerShell
    runs-on: windows-latest
    steps:
      - name: Checkout Bucket
        uses: actions/checkout@main
        with:
          fetch-depth: 2
          path: my_bucket
      - name: Checkout Scoop
        uses: actions/checkout@main
        with:
          repository: ScoopInstaller/Scoop
          path: scoop_core
      - name: Init Test Suite
        uses: potatoqualitee/psmodulecache@v5.1
        with:
          modules-to-cache: BuildHelpers
          shell: powershell
      - name: Test Bucket
        shell: powershell
        run: |
          $env:SCOOP_HOME="$(Convert-Path '.\scoop_core')"
          .\my_bucket\bin\test.ps1
  test_pwsh:
    name: PowerShell
    runs-on: windows-latest
    steps:
      - name: Checkout Bucket
        uses: actions/checkout@main
        with:
          fetch-depth: 2
          path: my_bucket
      - name: Checkout Scoop
        uses: actions/checkout@main
        with:
          repository: ScoopInstaller/Scoop
          path: scoop_core
      - name: Init Test Suite
        uses: potatoqualitee/psmodulecache@v5.1
        with:
          modules-to-cache: BuildHelpers
          shell: pwsh
      - name: Test Bucket
        shell: pwsh
        run: |
          $env:SCOOP_HOME="$(Convert-Path '.\scoop_core')"
          .\my_bucket\bin\test.ps1
