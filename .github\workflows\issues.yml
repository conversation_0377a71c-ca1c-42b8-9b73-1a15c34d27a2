on:
  issues:
    types: [opened, labeled]
name: Issues
jobs:
  issueHandler:
    name: <PERSON><PERSON><PERSON>ler
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@main
      - name: IssueHandler
        uses: ScoopInstaller/GithubActions@main
        if: github.event.action == 'opened' || (github.event.action == 'labeled' && contains(github.event.issue.labels.*.name, 'verify'))
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
