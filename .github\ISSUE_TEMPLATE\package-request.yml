name: 📦 Package Request
description: Open an issue about a missing package.
title: "[Request]: "
labels: ["package-request"]
body:
- type: checkboxes
  attributes:
    label: Prerequisites
    options:
    - label: I have searched all issues/PRs to ensure it has not already been reported or fixed.
      required: true
- type: checkboxes
  attributes:
    label: Criteria
    description: For a package to be acceptable in this bucket, it should be
    options:
    - label: Criteria 1
      required: true
    - label: Criteria 2
      required: true
    - label: Criteria 3
      required: true
- type: input
  attributes:
    label: Name
    description: Name of the package
  validations:
    required: true
- type: input
  attributes:
    label: Description
    description: Clear and concise details of what it is
  validations:
    required: true
- type: input
  attributes:
    label: Homepage
    description: URI of the package's homepage
  validations:
    required: true
- type: input
  attributes:
    label: Download Link(s)
    description: URI(s) of the package's download(s)
  validations:
    required: true
- type: textarea
  attributes:
    label: Some Indication of Popularity/Repute
    description: GitHub stars/software reviews etc.
  validations:
    required: true
